
# Set prefix to Ctrl-b
set-option -g prefix C-b
bind-key C-b send-prefix

# Enable mouse mode
set -g mouse on

# Improve colors
set -g default-terminal "tmux-256color"
set -ga terminal-features ",xterm-kitty:RGB"

# Split panes using | and -
bind | split-window -h
bind - split-window -v
unbind '"'
unbind %

# Status bar
set -g status-bg black
set -g status-fg white
set -g status-left '#[fg=green]#H'
set -g status-right '#[fg=cyan]🔋#(pmset -g batt | awk "/InternalBattery/ {print \$3}" | sed "s/;//") #[fg=yellow]💻#(top -l 1 | grep "CPU usage" | awk "{print \$3}") #[fg=green]📊#(memory_pressure | grep "System-wide memory free percentage" | awk "{print \$5}") #[fg=white]%H:%M'

# List of plugins
set -g @plugin 'tmux-plugins/tpm'
set -g @plugin 'tmux-plugins/tmux-resurrect'
set -g @plugin 'tmux-plugins/tmux-continuum'

# Automatically restore tmux environment
set -g @continuum-restore 'on'

# set -g @plugin 'dracula/tmux'

# Initialize TMUX plugin manager (keep this line at the very bottom of tmux.conf)
run '~/.tmux/plugins/tpm/tpm'
